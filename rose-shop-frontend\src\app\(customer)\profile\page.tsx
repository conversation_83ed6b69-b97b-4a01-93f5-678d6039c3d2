import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Mail, Phone, MapPin, Github, Linkedin, Twitter, ExternalLink, Download } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function UserProfile() {
  const skills = [
    "JavaScript",
    "TypeScript",
    "React",
    "Next.js",
    "Node.js",
    "Python",
    "AWS",
    "Docker",
    "PostgreSQL",
    "MongoDB",
    "Git",
    "Figma",
  ]

  const projects = [
    {
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution built with Next.js, Stripe integration, and PostgreSQL database.",
      technologies: ["Next.js", "TypeScript", "Stripe", "PostgreSQL"],
      image: "/placeholder.svg?height=200&width=300",
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      title: "Task Management App",
      description: "Collaborative task management application with real-time updates and team collaboration features.",
      technologies: ["React", "Node.js", "Socket.io", "MongoDB"],
      image: "/placeholder.svg?height=200&width=300",
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      title: "Data Analytics Dashboard",
      description: "Interactive dashboard for data visualization and analytics with real-time chart updates.",
      technologies: ["Python", "Django", "D3.js", "PostgreSQL"],
      image: "/placeholder.svg?height=200&width=300",
      liveUrl: "#",
      githubUrl: "#",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header Section */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
            <div className="relative">
              <Image
                src="/placeholder.svg?height=150&width=150"
                alt="User Profile"
                width={150}
                height={150}
                className="rounded-full border-4 border-white shadow-lg"
              />
              <div className="absolute bottom-2 right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
            </div>

            <div className="flex-1 text-center md:text-left">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">John Doe</h1>
              <p className="text-xl text-gray-600 mb-4">Rose Shop Customer</p>

              <div className="flex flex-wrap justify-center md:justify-start gap-4 text-sm text-gray-600 mb-6">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  <span>+****************</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>New York, NY</span>
                </div>
              </div>

              <div className="flex flex-wrap justify-center md:justify-start gap-3">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Download className="w-4 h-4 mr-2" />
                  Download Profile
                </Button>
                <Button variant="outline">
                  <Mail className="w-4 h-4 mr-2" />
                  Contact Support
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* About Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">About Me</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed mb-4">
                  Welcome to my profile! I'm a passionate customer of Rose Shop, where I find the most beautiful flowers for all occasions.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  I love exploring different flower arrangements and have been a loyal customer for over 2 years. Rose Shop always delivers exceptional quality and service.
                </p>
              </CardContent>
            </Card>

            {/* Order History */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Recent Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Wedding Bouquet</h3>
                    <p className="text-blue-600 font-medium mb-2">Order #12345 • March 15, 2024</p>
                    <p className="text-gray-700 mb-3">Beautiful white roses and baby's breath arrangement for wedding ceremony</p>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="secondary">Roses</Badge>
                      <Badge variant="secondary">Wedding</Badge>
                      <Badge variant="secondary">Delivered</Badge>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Birthday Arrangement</h3>
                    <p className="text-blue-600 font-medium mb-2">Order #12344 • March 10, 2024</p>
                    <p className="text-gray-700 mb-3">Colorful mixed flower arrangement for birthday celebration</p>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="secondary">Mixed Flowers</Badge>
                      <Badge variant="secondary">Birthday</Badge>
                      <Badge variant="secondary">Delivered</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            {/* Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Flower Preferences</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {["Roses", "Tulips", "Lilies", "Orchids", "Sunflowers", "Peonies"].map((flower, index) => (
                    <Badge key={index} variant="default" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                      {flower}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href="/orders" className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <FileText className="w-5 h-5 text-gray-700" />
                    <div>
                      <p className="font-medium">View Orders</p>
                      <p className="text-sm text-gray-600">Check your order history</p>
                    </div>
                  </Link>

                  <Link href="/profile/edit" className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <Mail className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Edit Profile</p>
                      <p className="text-sm text-gray-600">Update your information</p>
                    </div>
                  </Link>

                  <Link href="/profile/addresses" className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <MapPin className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-medium">Addresses</p>
                      <p className="text-sm text-gray-600">Manage delivery addresses</p>
                    </div>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Contact Card */}
            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Have questions about your orders or need assistance? We're here to help!
                </p>
                <Button className="w-full">
                  <Mail className="w-4 h-4 mr-2" />
                  Contact Support
                </Button>
              </CardContent>
            </Card>

            {/* Membership Status */}
            <Card>
              <CardHeader>
                <CardTitle>Membership Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="font-medium text-green-700">Premium Member</span>
                </div>
                <p className="text-sm text-gray-600">Enjoy exclusive discounts and priority delivery</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}